{"key": "group_werkzaamheden_rise_block", "title": "Werkzaamheden RISE Block", "fields": [{"key": "field_werkzaamheden_title", "label": "Titel", "name": "title", "aria-label": "", "type": "text", "instructions": "<PERSON>ite<PERSON> van het werkzaamheden blok", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "<PERSON><PERSON> we<PERSON>en kunnen onder andere bestaan uit:", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_werkzaamheden_list", "label": "Werkzaamheden", "name": "werkzaamheden", "aria-label": "", "type": "repeater", "instructions": "<PERSON><PERSON><PERSON> van werkzaamheden", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "collapsed": "", "min": 0, "max": 0, "layout": "table", "button_label": "<PERSON><PERSON><PERSON> werk<PERSON><PERSON><PERSON><PERSON> toe", "rows_per_page": 20, "sub_fields": [{"key": "field_werkzaamheden_item", "label": "Werk<PERSON><PERSON><PERSON><PERSON>", "name": "item", "aria-label": "", "type": "text", "instructions": "", "required": 1, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": "", "parent_repeater": "field_werkzaamheden_list"}]}, {"key": "field_werkzaamheden_anchor", "label": "<PERSON><PERSON>", "name": "anchor", "aria-label": "", "type": "text", "instructions": "Optioneel anker voor navigatie", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}], "location": [[{"param": "block", "operator": "==", "value": "acf/werkzaamheden-rise-block"}]], "menu_order": 0, "position": "normal", "style": "default", "label_placement": "top", "instruction_placement": "label", "hide_on_screen": "", "active": true, "description": "", "show_in_rest": 0, "modified": 1759866319}
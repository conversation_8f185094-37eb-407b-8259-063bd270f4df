$(document).ready(function(){
    $(document).on("initPage", function(){
        setConfettiMouse();
    });
});

function setConfettiMouse () {
    $(document).on("click", function(e) {
        const confettiContainer = document.getElementById("confettiContainer");
        const confettiCount = Math.floor(Math.random() * (50 - 20 + 1)) + 20;
        const colors = ["#123F77", "#F8B403"];
        const duration = 1;
        const size = 8;

        // Creëer confetti
        for (let i = 0; i < confettiCount; i++) {
            const confetti = document.createElement("div");
            confetti.className = "confetti";
            confetti.style.backgroundColor = colors[i % colors.length];
            confettiContainer.appendChild(confetti);

            const startX = e.clientX - size / 2;
            const startY = e.clientY - size / 2;

            gsap.set(confetti, {
                x: startX,
                y: startY,
                opacity: 1,
                scale: 1,
            });

            // Random eindpositie en rotatie
            const angle = Math.random() * Math.PI * 2; 
            const distance = Math.random() * 150 + 50;
            const endX = startX + Math.cos(angle) * distance;
            const endY = startY + Math.sin(angle) * distance;

            // Animeren met GSAP
            gsap.to(confetti, {
                x: endX,
                y: endY,
                opacity: 0,
                scale: 0.5,
                rotation: Math.random() * 360,
                duration: duration,
                ease: "power1.out",
                onComplete: () => confetti.remove(),
            });
        }
    });
}

// out: false
* {
  box-sizing: border-box;
  cursor:default;
  letter-spacing: 0;
  margin:0;
  padding:0;
  position:relative;
  &::selection {
    background: @primaryColor;
    color: @hardWhite;
  }
  &::-webkit-selection {
    background: @primaryColor;
    color: @hardWhite;
  }
}

html.lenis {
  height: auto;
}

.lenis.lenis-smooth {
  scroll-behavior: auto;
}

.lenis.lenis-smooth [data-lenis-prevent] {
  overscroll-behavior: contain;
}

.lenis.lenis-stopped {
  overflow: hidden;
}

.lenis.lenis-scrolling iframe {
  pointer-events: none;
}
html {
  overflow-x: hidden;
}
body {
  background: @backgroundColor;
  color: @almostBlack;
  font-family: 'Cal<PERSON>ri', arial, sans-serif;
  overflow-x: hidden;
  font-size: @vw22;
  line-height: 1;
  width: 100vw;
  strong {
    font-style: italic;
    font-weight: 800;
  }
  a {
    color: @primaryColor;
    cursor: pointer;
    text-decoration: none;
    .transition(.15s);
    * {
      cursor: pointer;
    }
  }
  p {
    font-family: '<PERSON><PERSON>ri', arial, sans-serif;
    font-size: @vw22;
    color: @almostBlack;
    font-weight: 100;
  }
}

[data-scroll-section] {
  background: @hardWhite;
  // padding: @vw100 + @vw30 + @vw5 0 0 0;
}

[data-scroll-container] {
  position: absolute;
  top: 0;
  width: 100%;
}

section {
  margin: @vw46 0;
  &:first-child {
    margin-top: 0;
  }
  &.blue {
    padding: @vw100 + @vw20 0;
    position: relative;
    color: @hardWhite;
    &:before {
      content: '';
      position: absolute;
      top: 0;
      height: 100%;
      left: @vw60;
      width: calc(100% ~"-" @vw120);
      background: @primaryColor;
      .rounded(@vw40);
    }
  }
}

img {
  pointer-events: none;
}

*:focus{
  outline:none;
  } 

.contentWrapper {
  display: block;
  padding: 0 @vw100 + @vw80;
  margin: auto;
  &.small {
    padding: 0 (@vw112 * 3) + (@vw16 * 2) + @vw104;
  }
}

.button {
  cursor: pointer;
  padding: @vw22 @vw40;
  background: @primaryColor;
  text-align: center;
  font-weight: 500; 
  .rounded(@vw40);
  text-decoration: none;
  color: @hardWhite; 
  position: relative;
  overflow: hidden;
  display: inline-block;
  border: 1px solid @primaryColor;
  transition:color .3s, background .3s, border-color .3s;
  -webkit-transition:color .3s, background .3s, border-color .3s;
  span, i {
    pointer-events: none;
  }
  &.outline {
    background: rgba(255,255,255,0);
    color: @hardWhite;
    border-color: @hardWhite;
  }
  &.secondary {
    background: rgba(255,255,255,0);
    color: @secondaryColor;
    border-color: @secondaryColor;
  }
  &:hover {
    color: @primaryColor;
    background: @hardWhite;
    border: 1px solid @hardWhite;
  }
  i {
    display: inline-block;
    vertical-align: top;
    width: @vw25;
  }
  .innerText {
    padding-left: @vw15;
    display: inline-block;
    position: relative;
    vertical-align: top;
    width: calc(100% ~"-" @vw40);
  }
}

@media all and (max-width: 1160px) {
  body {
    font-size: @vw22-1160;
  }
  section {
    margin: @vw100-1160 0;
    &.blue {
      padding: @vw100-1160 0;
      &:before {
        left: @vw20-1160;
        width: calc(100% ~"-" @vw40-1160);
        .rounded(@vw40-1160);
      }
    }
  }
  .contentWrapper {
    padding: 0 @vw60-1160;
    &.small {
      padding: 0 @vw120-1160;
    }
  }
  .button {
    padding: @vw22-1160 @vw40-1160;
    .rounded(@vw40-1160);
    i {
      width: @vw25-1160;
    }
    .innerText {
      padding-left: @vw15-1160;
      width: calc(100% ~"-" @vw40-1160);
    }
  }
}

@media all and (max-width: 580px) {
  body {
    font-size: @vw22-580;
  }
  section {
    margin: @vw100-580 + @vw20-580 0;
    &.blue {
      padding: @vw100-580 + @vw20-580 0;
      &:before {
        left: @vw20-580;
        width: calc(100% ~"-" @vw40-580);
        .rounded(@vw40-580);
      }
    }
  }
  .contentWrapper {
    padding: 0 @vw40-580;
    &.small {
      padding: 0 @vw40-580;
    }
  }
  .button {
    padding: @vw22-580 @vw40-580;
    .rounded(@vw40-580);
    i {
      width: @vw25-580;
    }
    .innerText {
      padding-left: @vw15-580;
      width: calc(100% ~"-" @vw40-580);
    }
  }
}
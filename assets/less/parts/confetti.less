// out: false
#confettiContainer {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 2;
    .confetti {
      position: absolute;
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background-color: transparent;
      opacity: 0;
    }
  
    .confetti:nth-child(even) {
      background-color: @primaryColor;
    }
  
    .confetti:nth-child(odd) {
      background-color: @secondaryColor;
    }
  }
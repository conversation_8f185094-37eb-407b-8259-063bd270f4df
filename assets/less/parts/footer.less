// out: false
footer {
    padding-bottom: @vw25;
    &.inview {
        .divider {
            width: 100%;
        }
        img {
            .transform(translateY(0) rotate(0deg), skewX(0deg), scale(1));
        }
    }
    .topCols {
        margin-left: -@vw8;
        width: calc(100% ~"+" @vw16);
        .col {
            display: inline-block;
            vertical-align: middle;
            margin: 0 @vw8;
            width: calc(40% ~"-" @vw16);
            &.small {
                width: calc(20% ~"-" @vw16);
            }
            &:last-child {
                .divider {
                    background: linear-gradient(90deg, @primaryColor, @secondaryColor);
                    .transitionMore(width, .6s, .75s, cubic-bezier(0.5, 1, 0.89, 1));
                }
            }
        }
    }
    img {
        display: block;
        object-fit: contain;
        width: 100%;
        height: auto;
        .transform(translateY(@vw50) rotate(20deg), skewX(5deg), scale(.4));
        .transitionMore(transform, .45s, .15s, cubic-bezier(0.87, 0, 0.13, 1));
    }
    .bottomCols {
        .col {
            display: inline-block;
            vertical-align: middle;
            width: 50%;
            &:last-child {
                text-align: right;
            }
        }
    }
    .divider {
        position: absolute;
        top: 50%;
        height: 3px;
        .rounded(3px);
        left: 0;
        width: 0%;
        .transform(translateY(-50%));
        .transitionMore(width, .6s, .3s, cubic-bezier(0.83, 0, 0.17, 1));
        background: linear-gradient(-90deg, @primaryColor, @secondaryColor);
    }
    .social {
        display: inline-block;
        vertical-align: middle;
        .transitionMore(opacity, .3s);
        color: @secondaryColor;
        &:not(:last-child) {
            margin-right: @vw20;
        }
        &:hover {
            opacity: .4;
        }
    }
}

footer {
    padding-bottom: @vw25-1160;
    &.inview {
        .divider {
            width: 100%;
        }
        img {
            .transform(translateY(0) rotate(0deg), skewX(0deg), scale(1));
        }
    }
    .topCols {
        margin-left: -@vw8-1160;
        width: calc(100% ~"+" @vw16-1160);
        .col {
            display: inline-block;
            vertical-align: middle;
            margin: 0 @vw8-1160;
            width: calc(40% ~"-" @vw16-1160);
            &.small {
                width: calc(20% ~"-" @vw16-1160);
            }
            &:last-child {
                .divider {
                    background: linear-gradient(90deg, @primaryColor, @secondaryColor);
                    .transitionMore(width, .6s, .75s, cubic-bezier(0.5, 1, 0.89, 1));
                }
            }
        }
    }
    img {
        display: block;
        object-fit: contain;
        width: 100%;
        height: auto;
        .transform(translateY(@vw50-1160) rotate(20deg), skewX(5deg), scale(.4));
        .transitionMore(transform, .45s, .15s, cubic-bezier(0.87, 0, 0.13, 1));
    }
    .bottomCols {
        .col {
            display: inline-block;
            vertical-align: middle;
            width: 50%;
            &:last-child {
                text-align: right;
            }
        }
    }
    .divider {
        position: absolute;
        top: 50%;
        height: 3px;
        .rounded(3px);
        left: 0;
        width: 0%;
        .transform(translateY(-50%));
        .transitionMore(width, .6s, .3s, cubic-bezier(0.83, 0, 0.17, 1));
        background: linear-gradient(-90deg, @primaryColor, @secondaryColor);
    }
    .social {
        display: inline-block;
        vertical-align: middle;
        .transitionMore(opacity, .3s);
        color: @secondaryColor;
        &:not(:last-child) {
            margin-right: @vw20-1160;
        }
        &:hover {
            opacity: .4;
        }
    }
}

footer {
    padding-bottom: @vw25-1160;
    &.inview {
        .divider {
            width: 100%;
        }
        img {
            .transform(translateY(0) rotate(0deg), skewX(0deg), scale(1));
        }
    }
    .topCols {
        display: flex;
        margin-left: -@vw8-1160;
        width: calc(100% + @vw16-1160);
        .col {
            display: inline-block;
            vertical-align: middle;
            margin: 0 @vw8-1160;
            width: calc(40% - @vw16-1160);
            &.small {
                width: calc(20% - @vw16-1160);
            }
            &:last-child {
                .divider {
                    background: linear-gradient(90deg, @primaryColor, @secondaryColor);
                    .transitionMore(width, .6s, .75s, cubic-bezier(0.5, 1, 0.89, 1));
                }
            }
        }
    }
    img {
        display: block;
        object-fit: contain;
        width: 100%;
        height: auto;
        .transform(translateY(@vw50-1160) rotate(20deg), skewX(5deg), scale(.4));
        .transitionMore(transform, .45s, .15s, cubic-bezier(0.87, 0, 0.13, 1));
    }
    .bottomCols {
        .col {
            display: inline-block;
            vertical-align: middle;
            width: 50%;
            &:last-child {
                text-align: right;
            }
        }
    }
    .divider {
        position: absolute;
        top: 50%;
        height: 3px;
        .rounded(3px);
        left: 0;
        width: 0%;
        .transform(translateY(-50%));
        .transitionMore(width, .6s, .3s, cubic-bezier(0.83, 0, 0.17, 1));
        background: linear-gradient(-90deg, @primaryColor, @secondaryColor);
    }
    .social {
        display: inline-block;
        vertical-align: middle;
        .transitionMore(opacity, .3s);
        color: @secondaryColor;
        &:not(:last-child) {
            margin-right: @vw20-1160;
        }
        &:hover {
            opacity: .4;
        }
    }
}

@media all and (max-width: 1160px) {
    footer {
        padding-bottom: @vw25-1160;
        .topCols {
            margin-left: -@vw8-1160;
            width: calc(100% + @vw16-1160);
            .col {
                margin: 0 @vw8-1160;
                width: calc(40% - @vw16-1160);
                &.small {
                    width: calc(20% - @vw16-1160);
                }
            }
        }
        img {
            .transform(translateY(@vw50-1160) rotate(20deg), skewX(5deg), scale(.4));
        }
        .social {
            &:not(:last-child) {
                margin-right: @vw20-1160;
            }
        }
    }
}

@media all and (max-width: 580px) {
    footer {
        padding-bottom: @vw25-580;
        .topCols {
            margin-bottom: @vw20-580;
            margin-left: -@vw8-580;
            width: calc(100% + @vw16-580);
            .col {
                margin: 0 @vw8-580;
                width: calc(25% - @vw16-580);
                &.small {
                    width: calc(50% - @vw16-580);
                }
                .divider {
                    background: @primaryColor !important;
                }
            }
        }
        img {
            .transform(translateY(@vw50-580) rotate(20deg), skewX(5deg), scale(.4));
        }
        .socials {
            margin-top: @vw40-580;
        }
        .social {
            color: @primaryColor;
            &:not(:last-child) {
                font-size: @vw35-580;
                margin-right: @vw30-580;
            }
        }
        .bottomCols {
            margin-top: @vw20-580;
            .col {
                text-align: center;
                width: 100%;
                &:last-child {
                    text-align: center;
                }
            }
        }
    }
}
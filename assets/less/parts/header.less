// out: false
header {
    position: fixed;
    padding: @vw26 0 @vw26 0;
    top: 0;
    width: 100%;
    background: @hardWhite;
    left: 0;
    z-index: 99;
    opacity: 0;
    &:after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        background: @secondaryColor;
        width: 100%;
        height: @vw13;
    }
    .transform(translateY(-100%) rotate(10deg) skewX(-10deg) scale(.5));
    &.active {
      .transform(translateY(0) rotate(0deg) skewX(0deg) scale(1));
      opacity: 1;
      transition: opacity .45s ease-in-out, transform .6s cubic-bezier(0.34, 1.56, 0.64, 1);
    }
    .anchorMenu {
        display: inline-block;
        margin-left: @vw80;
        vertical-align: middle;
        list-style: none;
        a {
            display: inline-block;
            vertical-align: middle;
            padding: @vw10;
            cursor: pointer;
            color: @hardWhite;
            text-decoration: none;
            transition: opacity .3s, transform .3s;
            &:not(:last-of-type) {
                margin-right: @vw22;
            }
            &:hover {
                opacity: .4;
            }
        }
    }
    .col {
        display: inline-block;
        width: 40%;
        vertical-align: middle;
        &:last-child {
            text-align: right;
            width: 60%;
        } 
        .logo {
            display: inline-block;
            width: @vw100 + @vw50;
            height: auto;
            vertical-align: middle;
            opacity: 1;
            .transition(.3s);
            &:hover {
                opacity: .4;
            }
            svg {
                width: @vw80;
                height: auto;
                object-fit: contain;
            }
        }
        ul {
          list-style: none;
          display: flex;
          margin-left: auto;
          justify-content: flex-end;
          gap: @vw44;
          flex-direction: row;
          li {
            a {
              font-family: 'Bree Serif'; 
              font-weight: 600;
              line-height: 1;
            }
          }
        }
    }
}

  @media all and (max-width: 1160px) {
  header {
    padding-top: @vw10-1160;
    .anchorMenu {
      margin-left: @vw40-1160;
      a {
        padding: @vw10-1160;
        &:not(:last-of-type) {
          margin-right: @vw22-1160;
        }
      }
    }
    .col {
      .logo {
        width: @vw100-1160 + @vw50-1160;
      }
      .socials {
        .social {
          padding: @vw10-1160;
        }
      }
      .button {
        margin-left: @vw40-1160;
      }
    }
  }
}

@media all and (max-width: 580px) {
  header {
    padding-top: @vw10-580;
    .anchorMenu {
     display: none;
    }
    .col {
      width: 25%;
      &:last-child {
        width: 75%;
      }
      .logo {
        width: @vw100-580 + @vw50-580;
      }
      .socials {
        display: none;
      }
      .button {
        margin-left: @vw40-580;
      }
    }
  }
}

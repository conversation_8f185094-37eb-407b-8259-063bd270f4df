// RISE & Partners page styling
// Hero Section
.heroSection {
    position: relative;
    background: @primaryColor;
    color: @hardWhite;
    padding: @vw120 0 @vw80 0;
    
    .heroBackground {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-image: url('../img/hero-bg.jpg'); // Add hero background image
        background-size: cover;
        background-position: center;
        opacity: 0.3;
    }
    
    .contentWrapper {
        position: relative;
        z-index: 2;
    }
    
    .hugeTitle {
        font-family: 'Screamer';
        font-size: @vw120;
        font-weight: 600;
        text-transform: uppercase;
        line-height: 1.1;
        color: @hardWhite;
        margin: 0;
    }
    
    .heroTitle {
        font-family: 'Screamer';
        font-size: @vw100;
        font-weight: 600;
        text-transform: uppercase;
        line-height: 1.1;
        color: @hardWhite;
        margin: 0 0 @vw30 0;
    }
    
    .heroText {
        font-size: @vw24;
        line-height: 1.4;
        max-width: @vw594; // Using existing vw variable
    }
}

// Main Content Layout
.mainContent {
    padding: @vw80 0;
    
    .cols {
        .flexbox();
        align-items: flex-start;
        gap: @vw60;
    }
    
    .mainCol {
        flex: 1;
        max-width: calc(70% - @vw30);
    }
    
    .sidebarCol {
        width: @vw276; // Using existing vw variable
        flex-shrink: 0;
    }
}

// Content Sections
.introText, .persoonText, .introSection .introText, .onderblokText {
    font-size: @vw22;
    line-height: 1.6;
    margin-bottom: @vw40;
    
    p {
        margin-bottom: @vw20;
        &:last-child {
            margin-bottom: 0;
        }
    }
}

.persoonSection {
    margin: @vw60 0;
    
    .normalTitle {
        font-family: 'Screamer';
        font-size: @vw60;
        font-weight: 600;
        text-transform: uppercase;
        line-height: 1.1;
        margin-bottom: @vw30;
        color: @primaryColor;
    }
}

.introSection {
    margin-bottom: @vw60;
    
    .normalTitle {
        font-family: 'Screamer';
        font-size: @vw60;
        font-weight: 600;
        text-transform: uppercase;
        line-height: 1.1;
        margin-bottom: @vw30;
        color: @primaryColor;
    }
}

// Opleidingen Section
.opleidingenSection {
    margin: @vw60 0;
    background: #87CEEB; // Light blue background
    padding: @vw40;
    .rounded(@vw20);
    
    .opleidingenTitle {
        font-family: 'Screamer';
        font-size: @vw32;
        font-weight: 600;
        text-transform: uppercase;
        margin-bottom: @vw20;
        color: @primaryColor;
    }
    
    .opleidingenList {
        .opleidingItem {
            font-size: @vw20;
            line-height: 1.4;
            margin-bottom: @vw10;
            color: @primaryColor;
            font-weight: 500;
            
            &:last-child {
                margin-bottom: 0;
            }
        }
    }
}

// Werkzaamheden Section (Blue section)
.werkzaamhedenSection {
    margin: @vw60 0;
    background: #87CEEB; // Light blue background
    padding: @vw50;
    .rounded(@vw20);
    position: relative;
    
    .werkzaamhedenTitle {
        font-family: 'Screamer';
        font-size: @vw36;
        font-weight: 600;
        text-transform: uppercase;
        line-height: 1.2;
        margin-bottom: @vw30;
        color: @primaryColor;
    }
    
    .werkzaamhedenList {
        list-style: none;
        padding: 0;
        margin: 0;
        
        .werkzaamhedenItem {
            position: relative;
            padding-left: @vw30;
            margin-bottom: @vw15;
            font-size: @vw20;
            line-height: 1.4;
            color: @primaryColor;
            
            &:before {
                content: '•';
                position: absolute;
                left: 0;
                color: @primaryColor;
                font-weight: bold;
                font-size: @vw24;
            }
            
            &:last-child {
                margin-bottom: 0;
            }
        }
    }
}

// Quote Section
.quoteSection {
    margin: @vw60 0;
    text-align: center;
    
    .quoteText {
        font-family: 'DancingScript';
        font-size: @vw36;
        font-style: italic;
        color: @primaryColor;
        margin: 0;
        line-height: 1.3;
    }
}

// Contact Card (Sidebar)
.contactCard {
    background: @hardWhite;
    border: 2px solid #E0E0E0;
    .rounded(@vw15);
    padding: @vw30;
    
    .contactTitle {
        font-family: 'Screamer';
        font-size: @vw24;
        font-weight: 600;
        text-transform: uppercase;
        color: @primaryColor;
        margin-bottom: @vw20;
    }
    
    .contactName {
        font-size: @vw20;
        font-weight: 600;
        color: @primaryColor;
        margin-bottom: @vw15;
    }
    
    .contactAddress {
        font-size: @vw18;
        line-height: 1.4;
        color: @primaryColor;
        margin-bottom: @vw15;
    }
    
    .contactPhone, .contactEmail {
        font-size: @vw18;
        margin-bottom: @vw10;
        
        a {
            color: @primaryColor;
            text-decoration: none;
            .transition(.15s);
            
            &:hover {
                color: @secondaryColor;
            }
        }
        
        &:last-child {
            margin-bottom: 0;
        }
    }
}

// Onderblok Section
.onderblokSection {
    margin: @vw60 0 0 0;

    .onderblokText {
        font-size: @vw22;
        line-height: 1.6;
        color: @primaryColor;
    }
}

// Tablet Media Queries (1160px)
@media all and (max-width: 1160px) {
    .heroSection {
        padding: @vw80-1160 0 @vw60-1160 0;

        .hugeTitle {
            font-size: @vw100-1160;
        }

        .heroTitle {
            font-size: @vw80-1160;
            margin-bottom: @vw25-1160;
        }

        .heroText {
            font-size: @vw22-1160;
        }
    }

    .mainContent {
        padding: @vw60-1160 0;

        .cols {
            gap: @vw40-1160;
        }

        .mainCol {
            max-width: calc(65% - @vw20-1160);
        }

        .sidebarCol {
            width: @vw276-1160; // Using existing vw variable
        }
    }

    .introText, .persoonText, .introSection .introText, .onderblokText {
        font-size: @vw20-1160;
        margin-bottom: @vw30-1160;

        p {
            margin-bottom: @vw18-1160;
        }
    }

    .persoonSection, .introSection {
        margin: @vw50-1160 0;

        .normalTitle {
            font-size: @vw50-1160;
            margin-bottom: @vw25-1160;
        }
    }

    .opleidingenSection {
        margin: @vw50-1160 0;
        padding: @vw30-1160;

        .opleidingenTitle {
            font-size: @vw28-1160;
            margin-bottom: @vw18-1160;
        }

        .opleidingenList .opleidingItem {
            font-size: @vw18-1160;
            margin-bottom: @vw8-1160;
        }
    }

    .werkzaamhedenSection {
        margin: @vw50-1160 0;
        padding: @vw40-1160;

        .werkzaamhedenTitle {
            font-size: @vw30-1160;
            margin-bottom: @vw25-1160;
        }

        .werkzaamhedenList .werkzaamhedenItem {
            padding-left: @vw25-1160;
            margin-bottom: @vw12-1160;
            font-size: @vw18-1160;

            &:before {
                font-size: @vw20-1160;
            }
        }
    }

    .quoteSection {
        margin: @vw50-1160 0;

        .quoteText {
            font-size: @vw30-1160;
        }
    }

    .contactCard {
        padding: @vw25-1160;

        .contactTitle {
            font-size: @vw20-1160;
            margin-bottom: @vw18-1160;
        }

        .contactName {
            font-size: @vw18-1160;
            margin-bottom: @vw12-1160;
        }

        .contactAddress {
            font-size: @vw16-1160;
            margin-bottom: @vw12-1160;
        }

        .contactPhone, .contactEmail {
            font-size: @vw16-1160;
            margin-bottom: @vw8-1160;
        }
    }

    .onderblokSection {
        margin: @vw50-1160 0 0 0;

        .onderblokText {
            font-size: @vw20-1160;
        }
    }
}

// Mobile Media Queries (580px)
@media all and (max-width: 580px) {
    .heroSection {
        padding: @vw60-580 0 @vw40-580 0;

        .hugeTitle {
            font-size: @vw80-580;
        }

        .heroTitle {
            font-size: @vw60-580;
            margin-bottom: @vw20-580;
        }

        .heroText {
            font-size: @vw20-580;
        }
    }

    .mainContent {
        padding: @vw40-580 0;

        .cols {
            flex-direction: column;
            gap: @vw30-580;
        }

        .mainCol {
            max-width: 100%;
        }

        .sidebarCol {
            width: 100%;
            order: -1; // Move sidebar to top on mobile
        }
    }

    .introText, .persoonText, .introSection .introText, .onderblokText {
        font-size: @vw18-580;
        margin-bottom: @vw25-580;

        p {
            margin-bottom: @vw15-580;
        }
    }

    .persoonSection, .introSection {
        margin: @vw40-580 0;

        .normalTitle {
            font-size: @vw40-580;
            margin-bottom: @vw20-580;
        }
    }

    .opleidingenSection {
        margin: @vw40-580 0;
        padding: @vw25-580;

        .opleidingenTitle {
            font-size: @vw24-580;
            margin-bottom: @vw15-580;
        }

        .opleidingenList .opleidingItem {
            font-size: @vw16-580;
            margin-bottom: @vw6-580;
        }
    }

    .werkzaamhedenSection {
        margin: @vw40-580 0;
        padding: @vw30-580;

        .werkzaamhedenTitle {
            font-size: @vw26-580;
            margin-bottom: @vw20-580;
        }

        .werkzaamhedenList .werkzaamhedenItem {
            padding-left: @vw20-580;
            margin-bottom: @vw10-580;
            font-size: @vw16-580;

            &:before {
                font-size: @vw18-580;
            }
        }
    }

    .quoteSection {
        margin: @vw40-580 0;

        .quoteText {
            font-size: @vw26-580;
        }
    }

    .contactCard {
        padding: @vw20-580;

        .contactTitle {
            font-size: @vw18-580;
            margin-bottom: @vw15-580;
        }

        .contactName {
            font-size: @vw16-580;
            margin-bottom: @vw10-580;
        }

        .contactAddress {
            font-size: @vw14-580;
            margin-bottom: @vw10-580;
        }

        .contactPhone, .contactEmail {
            font-size: @vw14-580;
            margin-bottom: @vw6-580;
        }
    }

    .onderblokSection {
        margin: @vw40-580 0 0 0;

        .onderblokText {
            font-size: @vw18-580;
        }
    }
}

// out: false

section {
    &.inview {
        [data-split-text] {
            visibility: visible;
            .line {
                .transform(translateY(0) rotate(0deg) skewX(0deg) scale(1));
                .stacker(50, .05s);
                opacity: 1;
                transition: opacity .45s ease-in-out, transform .6s cubic-bezier(0.34, 1.56, 0.64, 1);
            }
        }
    }
}

[data-split-text] {
    visibility: hidden;
    .line {
        position: relative;
        will-change: transform; 
        -webkit-will-change: transform;
        .transform(translateY(@vw10) rotate(10deg) skewX(10deg) scale(.2));
        opacity: 0;
    }
}

.hugeTitle {
    font-family: 'Cal<PERSON>ri'; 
    font-size: @vw100;
    font-weight: 800;
    text-transform: uppercase;
    line-height: .9;
    &.white {
        color: @hardWhite;
    }
    strong, em {
        color: @secondaryColor;
        font-style: normal;
    }
}

.normalTitle {
    font-family: 'Bree Serif'; 
    font-size: @vw56;
    font-weight: 600;
    line-height: 1.1;
    font-style: italic;
    &.white {
        color: @hardWhite;
    }
    strong, em {
        color: @secondaryColor;
        font-style: normal;
        font-weight: 600;
    }
}

.quoteTitle {
    font-family: 'DancingScript'; 
    font-size: @vw32;
    line-height: 1;
    &.white {
        color: @hardWhite;
    }
}

@media all and (max-width: 1160px) {
    [data-split-text] {
        .line {
            .transform(translateY(@vw10-1160) rotate(10deg) skewX(10deg) scale(.2));
        }
    }

    .hugeTitle {
        font-size: @vw100-1160;
    }

    .normalTitle {
        font-size: @vw60-1160;
    }

    .quoteTitle {
        font-size: @vw32-1160;
    }
}

@media all and (max-width: 580px) {
    [data-split-text] {
        .line {
            .transform(translateY(@vw10-580) rotate(10deg) skewX(10deg) scale(.2));
        }
    }

    .hugeTitle {
        font-size: @vw60-580;
    }

    .normalTitle {
        font-size: @vw60-580;
        line-height: .9;
    }

    .quoteTitle {
        font-size: @vw32-580;
    }
}
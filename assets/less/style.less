// out: ../../style.css, compress: true, strictMath: true

/*
Theme Name: Rise & Parnters 
Author: <PERSON>
Version: 1.0.0 
*/
 
@import 'vw_values.less';
@import 'constants.less'; 
@import 'default.less';
@import 'parts/fonts.less';
@import 'parts/typo.less';
@import 'parts/header.less';
@import 'parts/footer.less';
@import 'parts/confetti.less';
 

// blocks
@import '../../blocks/less/header-block.less';
@import '../../blocks/less/quote-block.less';
@import '../../blocks/less/slider-block.less';
@import '../../blocks/less/cta-block.less';

// RISE & Partners blocks
@import '../../blocks/less/hero-rise-block.less';
@import '../../blocks/less/intro-rise-block.less';
@import '../../blocks/less/werkzaamheden-rise-block.less';
@import '../../blocks/less/quote-rise-block.less';
@import '../../blocks/less/contact-card-rise-block.less';
@import '../../blocks/less/opleidingen-rise-block.less';

::-webkit-scrollbar {
  width: @vw10;
} 

::-webkit-scrollbar-track {
  background: @hardWhite;
}
 
::-webkit-scrollbar-thumb {
  border-radius: @vw50; 
  background: rgba(0,0,0,.1);
}

.block__headline {
    padding: 20px 15px 30px; 
    background: #fafafa;
    text-align: center;
}
.block__headline-title {
    font-family: 'Arial', sans-serif;
    font-size: 30px;
    font-weight: bold;
    position: relative;
}
.block__headline-title:after {
    content: '';
    display: block;
    width: 40px; 
    height: 2px;
    background: #333;
    margin: 0 auto; 
}

html.has-scroll-smooth {
	backface-visibility: hidden;
	transform: translateZ(0);
  [data-load-container] {
  	position: fixed;
  	top: 0;
  	right: 0; 
  	bottom: 0; 
  	left: 0;
  	width: 100vw;
  }
}

// Swup
.transition-fade {
  transition: .75s;
  opacity: 1; 
}  

html.is-animating .transition-fade {
  opacity: 0;
}

@media all and (max-width: 1160px) { 
  ::-webkit-scrollbar { 
    width: @vw10-1160;
  }
}

@media all and (max-width: 580px) {
  ::-webkit-scrollbar {
    width: @vw10-580;
  }
}
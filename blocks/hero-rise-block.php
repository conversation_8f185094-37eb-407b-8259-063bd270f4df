<?php
$size = 'full'; // (thumbnail, medium, large, full or custom size)
$image = get_field("background_image");
$title = get_field("title");
$subtitle = get_field("subtitle");
?>
<section class="heroRiseBlock" data-init <?php if (get_field("anchor")): ?>data-anchor="<?php the_field("anchor") ?>"<?php endif; ?>>
    <div class="backgroundWrapper">
        <?php if( $image ): ?>
        <div class="background" data-parallax data-parallax-speed="-2">
            <img class="lazy" data-src="<?php echo esc_url($image["url"]); ?>" alt="<?php echo esc_attr($image['alt']); ?>" />
        </div>
        <?php endif; ?>
        <div class="overlay"></div>
    </div>
    <div class="contentWrapper">
        <div class="innerContent">
            <?php if( $title ): ?>
            <h1 class="hugeTitle white" data-split-text>
                <?php
                $allowed_tags = '<strong><em><br>';
                $filtered_content = strip_tags($title, $allowed_tags);
                echo nl2br($filtered_content);
                ?>
            </h1>
            <?php endif; ?>
            
            <?php if( $subtitle ): ?>
            <div class="heroSubtitle">
                <?php echo wpautop($subtitle); ?>
            </div>
            <?php endif; ?>
        </div>
    </div>
</section>

$(document).ready(function(){
    $(document).on("initPage", function() {
      setTourBlock();
    });
  });

  function setTourBlock() {
    $(document).on("click", ".tourBlock .button.loadMore", function(){
      $(this).parents(".tourBlock").addClass("allEvents");
    });

    $(document).on("click", ".tourBlock .button:not(.loadMore)", function(){
      $(this).parents(".tourBlock").addClass("allEvents");
    });

    // Start automatische updates elke 5 minuten
    startTourAutoUpdate();
  }

  function startTourAutoUpdate() {
    // Update elke 5 minuten (300000 ms)
    setInterval(function() {
      refreshTourData();
    }, 300000);

    // Ook een update na 30 seconden voor snelle feedback
    setTimeout(function() {
      refreshTourData();
    }, 30000);
  }

  function refreshTourData() {
    if (typeof ajax_url === 'undefined') {
      return;
    }

    $.ajax({
      url: ajax_url,
      type: 'POST',
      data: {
        action: 'refresh_tour_data'
      },
      success: function(response) {
        if (response.success && response.data) {
          updateTourList(response.data.events, response.data.show_block);
        }
      },
      error: function() {
        console.log('Tour data refresh failed');
      }
    });
  }

  function updateTourList(tourData, showBlock) {
    $('.tourBlock').each(function() {
      var $tourBlock = $(this);
      var $tourList = $tourBlock.find('.tourList');
      var $tourContainer = $tourBlock.find('.tour');
      var $buttonContainer = $tourBlock.find('.button').parent();

      if (!showBlock || tourData.length === 0) {
        // Geen events, verberg het hele block
        $tourBlock.fadeOut(300);
        return;
      }

      // Toon het block als het verborgen was
      if (!$tourBlock.is(':visible')) {
        $tourBlock.fadeIn(300);
      }

      // Bouw nieuwe lijst op
      var newHtml = '';
      $.each(tourData, function(index, event) {
        var extraClass = (index >= 12) ? ' class="hiddenEvent"' : '';
        newHtml += '<li' + extraClass + '>';
        newHtml += '<span class="smallTitle">' + event.date + '</span>';
        newHtml += '<span class="innerCol">' + event.title + '</span>';
        newHtml += '<span class="innerCol">' + event.location + '</span>';
        newHtml += '</li>';
      });

      // Update de lijst met fade effect
      $tourList.fadeOut(300, function() {
        $tourList.html(newHtml).fadeIn(300);

        // Update button
        var buttonHtml = '';
        if (tourData.length > 12) {
          buttonHtml = '<div class="button secondary loadMore">Laad meer</div>';
        }
        $buttonContainer.html(buttonHtml);

        // Reset allEvents class
        $tourBlock.removeClass('allEvents');

        // Trigger inview animatie opnieuw als nodig
        if ($tourBlock.hasClass('inview')) {
          $tourBlock.removeClass('inview');
          setTimeout(function() {
            $tourBlock.addClass('inview');
          }, 100);
        }
      });
    });
  }
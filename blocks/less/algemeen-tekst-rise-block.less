// out: false
.algemeenTekstRiseBlock {
    .contentWrapper {
        .innerContent {
            max-width: @vw946;
            margin: 0 auto;
            .algemeenContent {
                font-size: @vw24;
                line-height: 1.6;
                color: #333;
                
                p {
                    margin-bottom: @vw20;
                    &:last-child {
                        margin-bottom: 0;
                    }
                }
                
                strong {
                    font-weight: 600;
                    color: @primaryColor;
                }
                
                em {
                    font-style: italic;
                }
            }
        }
    }
}

@media all and (max-width: 1160px) {
    .algemeenTekstRiseBlock {
        padding: @vw80-1160 0;
        
        .contentWrapper {
            .innerContent {
                max-width: @vw946-1160;
                
                .algemeenContent {
                    font-size: @vw24-1160;
                    
                    p {
                        margin-bottom: @vw20-1160;
                    }
                }
            }
        }
    }
}

@media all and (max-width: 580px) {
    .algemeenTekstRiseBlock {
        padding: @vw80-580 0;
        
        .contentWrapper {
            .innerContent {
                max-width: @vw946-580;
                
                .algemeenContent {
                    font-size: @vw24-580;
                    
                    p {
                        margin-bottom: @vw20-580;
                    }
                }
            }
        }
    }
}

// out: false
.algemeneInfoRiseBlock {
    padding: @vw60 0;
    
    .contentWrapper {
        .algemeneInfoCard {
            background: @hardWhite;
            border: @vw2 solid #E5E5E5;
            .rounded(@vw12);
            padding: @vw40;
            max-width: @vw594;
            margin: 0 auto;
            .box-shadow(0 @vw4 @vw20 rgba(0, 0, 0, 0.1));
            
            .bedrijfsnaam {
                font-size: @vw28;
                font-weight: 600;
                color: @primaryColor;
                margin-bottom: @vw30;
                margin-top: 0;
                text-align: center;
            }
            
            .infoGrid {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: @vw20;
                
                .infoItem {
                    font-size: @vw18;
                    line-height: 1.4;
                    
                    strong {
                        color: @primaryColor;
                        font-weight: 600;
                        display: block;
                        margin-bottom: @vw8;
                    }
                    
                    a {
                        color: #333;
                        text-decoration: none;
                        .transition(0.3s);
                        
                        &:hover {
                            color: @primaryColor;
                            text-decoration: underline;
                        }
                    }
                    
                    p {
                        margin-bottom: @vw4;
                        &:last-child {
                            margin-bottom: 0;
                        }
                    }

                    .socialLinks {
                        a {
                            display: inline-block;
                            margin-right: @vw12;

                            &:last-child {
                                margin-right: 0;
                            }
                        }
                    }
                }
            }
        }
    }
}

@media all and (max-width: 1160px) {
    .algemeneInfoRiseBlock {
        padding: @vw60-1160 0;
        
        .contentWrapper {
            .algemeneInfoCard {
                border: @vw2-1160 solid #E5E5E5;
                .rounded(@vw12-1160);
                padding: @vw40-1160;
                max-width: @vw594-1160;
                .box-shadow(0 @vw4-1160 @vw20-1160 rgba(0, 0, 0, 0.1));
                
                .bedrijfsnaam {
                    font-size: @vw28-1160;
                    margin-bottom: @vw30-1160;
                }
                
                .infoGrid {
                    gap: @vw20-1160;
                    
                    .infoItem {
                        font-size: @vw18-1160;
                        
                        strong {
                            margin-bottom: @vw8-1160;
                        }
                        
                        p {
                            margin-bottom: @vw4-1160;
                        }

                        .socialLinks {
                            a {
                                margin-right: @vw12-1160;
                            }
                        }
                    }
                }
            }
        }
    }
}

@media all and (max-width: 580px) {
    .algemeneInfoRiseBlock {
        padding: @vw60-580 0;
        
        .contentWrapper {
            .algemeneInfoCard {
                border: @vw2-580 solid #E5E5E5;
                .rounded(@vw12-580);
                padding: @vw40-580;
                max-width: @vw594-580;
                .box-shadow(0 @vw4-580 @vw20-580 rgba(0, 0, 0, 0.1));
                
                .bedrijfsnaam {
                    font-size: @vw28-580;
                    margin-bottom: @vw30-580;
                }
                
                .infoGrid {
                    grid-template-columns: 1fr;
                    gap: @vw20-580;
                    
                    .infoItem {
                        font-size: @vw18-580;
                        
                        strong {
                            margin-bottom: @vw8-580;
                        }
                        
                        p {
                            margin-bottom: @vw4-580;
                        }

                        .socialLinks {
                            a {
                                margin-right: @vw12-580;
                            }
                        }
                    }
                }
            }
        }
    }
}

// out: false
.heroRiseBlock {
    position: relative;
    min-height: @vw650;
    .flexbox();
    align-items: center;
    justify-content: center;
    color: @hardWhite;
    overflow: hidden;
    
    .backgroundWrapper {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 1;
        
        .background {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            
            img {
                width: 100%;
                height: 100%;
                object-fit: cover;
                display: block;
            }
        }
        
        .overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(18, 63, 119, 0.7); // @primaryColor with opacity
            z-index: 2;
        }
    }
    
    .contentWrapper {
        position: relative;
        z-index: 3;
        text-align: center;
        
        .innerContent {
            max-width: @vw946;
            margin: 0 auto;
            
            .hugeTitle {
                margin-bottom: @vw40;
            }
            
            .heroSubtitle {
                font-size: @vw24;
                line-height: 1.4;
                opacity: 0.9;
                
                p {
                    margin-bottom: @vw20;
                    &:last-child {
                        margin-bottom: 0;
                    }
                }
            }
        }
    }
}

@media all and (max-width: 1160px) {
    .heroRiseBlock {
        min-height: @vw650-1160;
        
        .contentWrapper {
            .innerContent {
                max-width: @vw946-1160;
                
                .hugeTitle {
                    margin-bottom: @vw40-1160;
                }
                
                .heroSubtitle {
                    font-size: @vw24-1160;
                    
                    p {
                        margin-bottom: @vw20-1160;
                    }
                }
            }
        }
    }
}

@media all and (max-width: 580px) {
    .heroRiseBlock {
        min-height: @vw650-580;
        
        .contentWrapper {
            .innerContent {
                max-width: @vw946-580;
                
                .hugeTitle {
                    margin-bottom: @vw40-580;
                }
                
                .heroSubtitle {
                    font-size: @vw24-580;
                    
                    p {
                        margin-bottom: @vw20-580;
                    }
                }
            }
        }
    }
}

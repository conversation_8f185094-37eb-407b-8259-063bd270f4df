// out: false
.introRiseBlock {
    padding: @vw80 0;
    
    .contentWrapper {
        .innerContent {
            max-width: @vw946;
            margin: 0 auto;
            
            .normalTitle {
                margin-bottom: @vw40;
            }
            
            .introContent {
                font-size: @vw22;
                line-height: 1.6;
                color: #333;
                
                p {
                    margin-bottom: @vw20;
                    &:last-child {
                        margin-bottom: 0;
                    }
                }
                
                strong {
                    font-weight: 600;
                }
                
                em {
                    font-style: italic;
                }
            }
        }
    }
}

@media all and (max-width: 1160px) {
    .introRiseBlock {
        padding: @vw80-1160 0;
        
        .contentWrapper {
            .innerContent {
                max-width: @vw946-1160;
                
                .normalTitle {
                    margin-bottom: @vw40-1160;
                }
                
                .introContent {
                    font-size: @vw22-1160;
                    
                    p {
                        margin-bottom: @vw20-1160;
                    }
                }
            }
        }
    }
}

@media all and (max-width: 580px) {
    .introRiseBlock {
        padding: @vw80-580 0;
        
        .contentWrapper {
            .innerContent {
                max-width: @vw946-580;
                
                .normalTitle {
                    margin-bottom: @vw40-580;
                }
                
                .introContent {
                    font-size: @vw22-580;
                    
                    p {
                        margin-bottom: @vw20-580;
                    }
                }
            }
        }
    }
}

<?php
$message = get_field("message");
$button_label = get_field("button_label");
$image = get_field("image");
$video = get_field("video");

// Check eerst of er events zijn voordat we het block tonen
$current_date = date('Y-m-d');
$tour_posts = get_posts([
    'post_type' => 'tour_data',
    'posts_per_page' => -1,
    'meta_key' => 'event_date',
    'orderby' => 'meta_value',
    'order' => 'ASC',
    'meta_query' => [
        [
            'key' => 'event_date',
            'value' => $current_date,
            'compare' => '>=',
            'type' => 'DATE'
        ]
    ]
]);

// Toon het block alleen als er events zijn
if ($tour_posts): ?>

<section class="tourBlock blue" data-init <?php if (get_field("anchor")): ?>data-anchor="<?php the_field("anchor") ?>"<?php endif; ?>>
    <div class="contentWrapper">
        <div class="cols">
            <div class="col">
                <h2 class="normalTitle" data-split-text>
                    <?php
                    $content = get_field("title");
                    $allowed_tags = '<strong><em><br>';
                    $filtered_content = strip_tags($content, $allowed_tags);
                    echo nl2br($filtered_content);
                    ?>
                </h2>

                <div class="tour" data-message="<?php echo esc_attr($message); ?>">
                    <ul class="tourList">
                        <?php
                        $total_events = count($tour_posts);
                        foreach ($tour_posts as $index => $post):
                            $event_date = get_field('event_date', $post->ID);
                            $event_location = get_field('event_location', $post->ID);
                            if ($event_date) {
                                $formatted_date = date('j M. Y', strtotime($event_date));
                            } else {
                                $formatted_date = '';
                            }

                            // Voeg extra class toe voor items na de eerste 12
                            $extra_class = ($index >= 12) ? ' class="hiddenEvent"' : '';
                        ?>
                            <li<?php echo $extra_class; ?>>
                                <span class="smallTitle"><?php echo esc_html($formatted_date); ?></span>
                                <span class="innerCol">
                                    <?php echo esc_html($post->post_title); ?>
                                </span>
                                <span class="innerCol">
                                    <?php if ($event_location): echo esc_html($event_location); endif; ?>
                                </span>
                            </li>
                        <?php endforeach; ?>
                    </ul>
                </div>

                <?php if ($total_events > 12): ?>
                    <div class="button secondary loadMore">
                        Laad meer
                    </div>
                <?php elseif ($button_label): ?>
                    <div class="button secondary">
                        <?php echo esc_html($button_label); ?>
                    </div>
                <?php endif; ?>
            </div>
            <div class="col image">
                <div class="imageWrapper">
                    <div class="innerImage" data-parallax data-parallax-speed="1" data-scroll-direction="horizontal">
                        <?php if ($video): ?>
                        <video poster="<?php echo esc_url($image['url']); ?>" class="video" muted playsinline loop autoplay>
                            <source src="<?php echo esc_url($video); ?>" type="video/mp4">
                        </video>
                        <?php elseif( $image ): ?>
                        <img class="lazy" data-src="<?php echo esc_url($image["sizes"]['large']); ?>" alt="<?php echo esc_attr($image['alt']); ?>" />
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<?php endif; // Einde van de check of er events zijn ?>

<?php

function jn_enqueue_assets() {
    $scripts = array(
        'Jquery' => '/libs/jquery.min.js',
        'Lenis' => '/libs/lenis.min.js',
        'Swup' => '/libs/swup.js',
        'Swup_head' => '/libs/swup_head.js',
        'Swup_Gtag' => '/libs/swup_gtag.js',
        'Select2' => '/libs/select2.min.js',
        'GSAP' => '/libs/gsap.min.js',
        'FLICKITY' => '/libs/flickity.min.js',
        'Custom_Ease' => '/libs/CustomEase.min.js',
        'Split text' => '/libs/SplitText.min.js',
        'ScrollTrigger' => '/libs/ScrollTrigger.min.js',
        'Hammer_js' => '/libs/hammer.min.js',
        'main_js' => '/assets/js/main.js',
        'Header' => '/assets/js/header.js',
        'Confetti' => '/assets/js/parts/confetti.js',
        'Parallax' => '/assets/js/parts/parallax.js',
        'Slider' => '/assets/js/parts/slider.js',
        'Inview' => '/assets/js/parts/inview.js',
    );

    foreach ($scripts as $handle => $path) {
        wp_enqueue_script($handle, get_theme_file_uri($path), array(), '1.0', true);
    }

    $blocks = array(
        'HEADER BLOCK' => '/blocks/js/header-block.js',
        'TOUR BLOCK' => '/blocks/js/tour-block.js',
    );

    foreach ($blocks as $handle => $path) {
        wp_enqueue_script($handle, get_theme_file_uri($path), array(), '1.0', true);
    }

    wp_enqueue_style('main', get_stylesheet_uri());
    // wp_enqueue_style('select2', get_theme_file_uri('/libs/select2.min.css'), array(), '1.1', 'all');
}

add_action('wp_enqueue_scripts', 'jn_enqueue_assets');

// Add favicon
function ilc_favicon() {
    echo "<link rel='shortcut icon' href='" . get_stylesheet_directory_uri() . "/favicon.ico' />\n";
}

add_action('wp_head', 'ilc_favicon');

// Customize theme settings
function jn_customize_register($wp_customize) {
    $sections = array(
        'customTheme-main-callout-title' => 'Title',
        'customTheme-main-callout-description' => 'Description',
        'customTheme-main-callout-featured-image' => 'Image',
        'customTheme-main-callout-logo' => 'Logo',
        'customTheme-main-callout-logo-white' => 'Logo (White)',
        'customTheme-main-callout-telephone' => 'Telephone',
        'customTheme-main-callout-telephone-label' => 'Telephone label',
        'customTheme-main-callout-mail' => 'Mail',
        'customTheme-main-callout-address' => 'Address',
        'customTheme-main-callout-facebook' => 'Facebook URL',
        'customTheme-main-callout-linkedin' => 'LinkedIn URL',
        'customTheme-main-callout-instagram' => 'Instagram URL',
        'customTheme-main-callout-spotify' => 'Spotify URL',
        'customTheme-main-callout-youtube' => 'Youtube URL',
        'customTheme-main-callout-analytics' => 'Analytics ID',
    );

    $wp_customize->add_section('customTheme-main-callout-section', array(
        'title' => 'Main Information'
    ));

    foreach ($sections as $setting_id => $label) {
        $wp_customize->add_setting($setting_id);
        $control_args = array(
            'label' => $label,
            'section' => 'customTheme-main-callout-section',
            'settings' => $setting_id
        );

        if (strpos($setting_id, 'featured-image') !== false || strpos($setting_id, 'logo') !== false) {
            $control_args['width'] = 750;
            $control_args['height'] = 500;
            $wp_customize->add_control(new WP_Customize_Media_Control($wp_customize, $setting_id . '-control', $control_args));
        } elseif ($label === 'Description') {
            $control_args['type'] = 'textarea';
            $wp_customize->add_control(new WP_Customize_Control($wp_customize, $setting_id . '-control', $control_args));
        } else {
            $wp_customize->add_control(new WP_Customize_Control($wp_customize, $setting_id . '-control', $control_args));
        }
    }
}

add_action('customize_register', 'jn_customize_register');

// Register menus
function jn_register_menus() {
    register_nav_menus(array(
        'primary-menu' => 'Primary Menu'
    ));
}

add_action('after_setup_theme', 'jn_register_menus');

// Remove max image preview setting
add_filter('wp_robots', 'remove_max_image_preview_large', 10, 1);
function remove_max_image_preview_large($robots) {
    unset($robots['max-image-preview']);
    return $robots;
}

// blocks
add_action('acf/init', 'my_acf_blocks_init');
function my_acf_blocks_init() {

    // Check function exists.
    if( function_exists('acf_register_block_type') ) {

        // Register a testimonial block.
        acf_register_block_type(array(
            'name'              => 'header_block',
            'title'             => __('Header Block'),
            'render_template'   => 'blocks/header-block.php',
            'category'          => 'header',
        ));
        acf_register_block_type(array(
            'name'              => 'quote_block',
            'title'             => __('Quote Block'),
            'render_template'   => 'blocks/quote-block.php',
            'category'          => 'quote',
        ));
        acf_register_block_type(array(
            'name'              => 'tour_block',
            'title'             => __('Tour Block'),
            'render_template'   => 'blocks/tour-block.php',
            'category'          => 'tour',
        ));
        acf_register_block_type(array(
            'name'              => 'slider_block',
            'title'             => __('Slider Block'),
            'render_template'   => 'blocks/slider-block.php',
            'category'          => 'slider',
        ));
        acf_register_block_type(array(
            'name'              => 'cta_block',
            'title'             => __('CTA Block'),
            'render_template'   => 'blocks/cta-block.php',
            'category'          => 'cta',
        ));
    }
}

// Custom Post Type for Tour Data
function register_tour_data_post_type() {
    register_post_type('tour_data', [
        'labels' => [
            'name' => __('Tour Data'),
            'singular_name' => __('Tour Data'),
        ],
        'public' => true,
        'has_archive' => true,
        'supports' => ['title', 'editor'],
    ]);
}
add_action('init', 'register_tour_data_post_type');

function fetch_google_calendar_events() {
    error_log('Cronjob is uitgevoerd om ' . date('Y-m-d H:i:s'));

    $api_key = 'AIzaSyCGTZY-pFPArD71z3a7WJfVSoty5Ci-4Kg';
    $calendar_id = '<EMAIL>';

    // Haal alleen toekomstige events op
    $time_min = date('c'); // Huidige tijd in RFC3339 format
    $url = sprintf(
        'https://www.googleapis.com/calendar/v3/calendars/%s/events?key=%s&timeMin=%s&orderBy=startTime&singleEvents=true',
        urlencode($calendar_id),
        $api_key,
        urlencode($time_min)
    );

    $response = wp_remote_get($url);
    if (is_wp_error($response)) {
        error_log('Google Calendar API error: ' . $response->get_error_message());
        return;
    }

    $body = wp_remote_retrieve_body($response);
    $data = json_decode($body, true);

    if (isset($data['error'])) {
        error_log('Google Calendar API error: ' . $data['error']['message']);
        return;
    }

    // Haal alle huidige event IDs op uit de database
    $existing_event_ids = get_existing_event_ids();
    $current_event_ids = array();

    if (!empty($data['items'])) {
        foreach ($data['items'] as $event) {
            if (isset($event['id'])) {
                $current_event_ids[] = $event['id'];
                save_or_update_event($event);
            }
        }
    }

    // Verwijder events die niet meer in de calendar staan
    cleanup_removed_events($existing_event_ids, $current_event_ids);

    // Verwijder verouderde events (events die in het verleden liggen)
    cleanup_past_events();
}


function save_or_update_event($event) {
    $event_name = $event['summary'] ?? 'Untitled Event';
    $event_date = $event['start']['dateTime'] ?? $event['start']['date'];
    $event_location = $event['location'] ?? '';

    // Controleer of dit evenement al is opgeslagen
    $existing_posts = get_posts([
        'post_type' => 'tour_data',
        'meta_key' => 'event_id',
        'meta_value' => $event['id'],
        'posts_per_page' => 1,
    ]);

    if ($existing_posts) {
        // Update bestaand event
        $post_id = $existing_posts[0]->ID;

        // Check of er wijzigingen zijn
        $current_title = get_the_title($post_id);
        $current_date = get_field('event_date', $post_id);
        $current_location = get_field('event_location', $post_id);

        $needs_update = false;

        if ($current_title !== $event_name) {
            wp_update_post([
                'ID' => $post_id,
                'post_title' => $event_name,
            ]);
            $needs_update = true;
        }

        if ($current_date !== $event_date) {
            update_field('event_date', $event_date, $post_id);
            $needs_update = true;
        }

        if ($current_location !== $event_location) {
            update_field('event_location', $event_location, $post_id);
            $needs_update = true;
        }

        if ($needs_update) {
            error_log("Updated event: {$event_name} (ID: {$post_id})");
        }

    } else {
        // Maak een nieuwe post
        $post_id = wp_insert_post([
            'post_title'   => $event_name,
            'post_type'    => 'tour_data',
            'post_status'  => 'publish',
        ]);

        // Voeg ACF-velden toe
        if ($post_id && !is_wp_error($post_id)) {
            update_field('event_id', $event['id'], $post_id);
            update_field('event_date', $event_date, $post_id);
            update_field('event_location', $event_location, $post_id);
            error_log("Created new event: {$event_name} (ID: {$post_id})");
        }
    }
}

function get_existing_event_ids() {
    $posts = get_posts([
        'post_type' => 'tour_data',
        'posts_per_page' => -1,
        'fields' => 'ids',
    ]);

    $event_ids = array();
    foreach ($posts as $post_id) {
        $event_id = get_field('event_id', $post_id);
        if ($event_id) {
            $event_ids[$event_id] = $post_id;
        }
    }

    return $event_ids;
}

function cleanup_removed_events($existing_event_ids, $current_event_ids) {
    $removed_event_ids = array_diff(array_keys($existing_event_ids), $current_event_ids);

    foreach ($removed_event_ids as $removed_id) {
        $post_id = $existing_event_ids[$removed_id];
        wp_delete_post($post_id, true);
        error_log("Deleted removed event with ID: {$removed_id} (Post ID: {$post_id})");
    }
}

function cleanup_past_events() {
    $current_date = date('Y-m-d');

    $past_events = get_posts([
        'post_type' => 'tour_data',
        'posts_per_page' => -1,
        'meta_query' => [
            [
                'key' => 'event_date',
                'value' => $current_date,
                'compare' => '<',
                'type' => 'DATE'
            ]
        ]
    ]);

    foreach ($past_events as $event) {
        wp_delete_post($event->ID, true);
        error_log("Deleted past event: {$event->post_title} (ID: {$event->ID})");
    }
}

// Voeg custom intervals toe
function custom_cron_schedule($schedules) {
    $schedules['every_five_minutes'] = array(
        'interval' => 300, // 300 seconden = 5 minuten
        'display'  => __('Every 5 Minutes')
    );
    $schedules['every_fifteen_minutes'] = array(
        'interval' => 900, // 900 seconden = 15 minuten
        'display'  => __('Every 15 Minutes')
    );
    return $schedules;
}
add_filter('cron_schedules', 'custom_cron_schedule');

// Cronjob instellen
function schedule_google_calendar_sync() {
    if (!wp_next_scheduled('sync_google_calendar_events')) {
        wp_schedule_event(time(), 'every_five_minutes', 'sync_google_calendar_events');
    }
}
add_action('wp', 'schedule_google_calendar_sync');

add_action('sync_google_calendar_events', 'fetch_google_calendar_events');

// AJAX handler voor real-time tour updates
function ajax_refresh_tour_data() {
    // Voer een snelle sync uit
    fetch_google_calendar_events();

    // Haal de bijgewerkte tour data op
    $current_date = date('Y-m-d');
    $tour_posts = get_posts([
        'post_type' => 'tour_data',
        'posts_per_page' => -1,
        'meta_key' => 'event_date',
        'orderby' => 'meta_value',
        'order' => 'ASC',
        'meta_query' => [
            [
                'key' => 'event_date',
                'value' => $current_date,
                'compare' => '>=',
                'type' => 'DATE'
            ]
        ]
    ]);

    $tour_data = array();
    if ($tour_posts) {
        foreach ($tour_posts as $post) {
            $event_date = get_field('event_date', $post->ID);
            $event_location = get_field('event_location', $post->ID);
            $formatted_date = $event_date ? date('j M. Y', strtotime($event_date)) : '';

            $tour_data[] = [
                'id' => $post->ID,
                'title' => $post->post_title,
                'date' => $formatted_date,
                'location' => $event_location ?: ''
            ];
        }
    }

    wp_send_json_success([
        'events' => $tour_data,
        'total' => count($tour_data),
        'show_block' => count($tour_data) > 0
    ]);
}

add_action('wp_ajax_refresh_tour_data', 'ajax_refresh_tour_data');
add_action('wp_ajax_nopriv_refresh_tour_data', 'ajax_refresh_tour_data');

// Voeg AJAX URL toe aan frontend
function add_ajax_url_to_frontend() {
    ?>
    <script type="text/javascript">
        var ajax_url = '<?php echo admin_url('admin-ajax.php'); ?>';
    </script>
    <?php
}
add_action('wp_head', 'add_ajax_url_to_frontend');

// Admin menu voor handmatige sync
function add_tour_sync_admin_menu() {
    add_management_page(
        'Tour Sync',
        'Tour Sync',
        'manage_options',
        'tour-sync',
        'tour_sync_admin_page'
    );
}
add_action('admin_menu', 'add_tour_sync_admin_menu');

function tour_sync_admin_page() {
    if (isset($_POST['sync_now'])) {
        fetch_google_calendar_events();
        echo '<div class="notice notice-success"><p>Tour data gesynchroniseerd!</p></div>';
    }

    ?>
    <div class="wrap">
        <h1>Tour Synchronisatie</h1>
        <p>Handmatig synchroniseren van Google Calendar events met tour data.</p>

        <form method="post">
            <?php wp_nonce_field('tour_sync_action', 'tour_sync_nonce'); ?>
            <input type="submit" name="sync_now" class="button button-primary" value="Nu Synchroniseren">
        </form>

        <h2>Huidige Tour Events</h2>
        <?php
        $current_date = date('Y-m-d');
        $tour_posts = get_posts([
            'post_type' => 'tour_data',
            'posts_per_page' => -1,
            'meta_key' => 'event_date',
            'orderby' => 'meta_value',
            'order' => 'ASC',
            'meta_query' => [
                [
                    'key' => 'event_date',
                    'value' => $current_date,
                    'compare' => '>=',
                    'type' => 'DATE'
                ]
            ]
        ]);

        if ($tour_posts): ?>
            <table class="wp-list-table widefat fixed striped">
                <thead>
                    <tr>
                        <th>Event</th>
                        <th>Datum</th>
                        <th>Locatie</th>
                        <th>Event ID</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($tour_posts as $post):
                        $event_date = get_field('event_date', $post->ID);
                        $event_location = get_field('event_location', $post->ID);
                        $event_id = get_field('event_id', $post->ID);
                        ?>
                        <tr>
                            <td><?php echo esc_html($post->post_title); ?></td>
                            <td><?php echo esc_html($event_date ? date('j M. Y', strtotime($event_date)) : ''); ?></td>
                            <td><?php echo esc_html($event_location); ?></td>
                            <td><?php echo esc_html($event_id); ?></td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        <?php else: ?>
            <p>Geen toekomstige events gevonden.</p>
        <?php endif; ?>
    </div>
    <?php
}

?>

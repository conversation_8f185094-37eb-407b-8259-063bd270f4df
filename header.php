<!DOCTYPE html>
<html lang="nl" dir="ltr">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>
    <?php echo get_theme_mod('customTheme-main-callout-title') ?: get_bloginfo('name'); ?> |
    <?php the_title(); ?>
  </title>
  <meta name="robots" content="follow, index, max-snippet:-1, max-video-preview:-1, max-image-preview:large">
  <meta name="msapplication-TileColor" content="#00aba9">
  <meta name="theme-color" content="#ffffff">
  <meta name="description" content="<?php echo esc_attr(get_theme_mod('customTheme-main-callout-description')); ?>">
  <meta name="author" content="https://www.linkedin.com/in/dennisthemenace/">

  <!-- Open Graph Metadata -->
  <meta property="og:title" content="<?php echo get_theme_mod('customTheme-main-callout-title') ?: get_bloginfo('name'); ?> | <?php the_title(); ?>">
  <meta property="og:description" content="<?php echo esc_attr(get_theme_mod('customTheme-main-callout-description')); ?>">
  <meta property="og:image" content="<?php echo esc_url(wp_get_attachment_url(get_theme_mod('customTheme-main-callout-featured-image'))); ?>">
  <meta property="og:image:alt" content="<?php echo esc_attr(get_theme_mod('customTheme-main-callout-title')); ?>">
  <meta property="og:type" content="website">
  <meta property="og:url" content="<?php echo esc_url(get_permalink()); ?>">
  <meta property="og:site_name" content="<?php echo get_theme_mod('customTheme-main-callout-title') ?: get_bloginfo('name'); ?>">
  <meta property="og:locale" content="nl">

  <!-- Favicon and Icons -->
  <link rel="icon" type="image/png" href="/favicon-96x96.png" sizes="96x96">
  <link rel="icon" type="image/svg+xml" href="/favicon.svg">
  <link rel="shortcut icon" href="/favicon.ico">
  <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
  <meta name="apple-mobile-web-app-title" content="Naachtwaerk">
  <link rel="manifest" href="/site.webmanifest">

  <!-- JSON-LD Structured Data -->
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "<?php echo get_bloginfo('name'); ?>",
    "url": "<?php echo esc_url(home_url()); ?>",
    "logo": "<?php echo get_stylesheet_directory_uri(); ?>/logo.png",
    "contactPoint": {
      "@type": "ContactPoint",
      "telephone": "<?php echo esc_html(get_theme_mod('customTheme-main-callout-telephone')); ?>",
      "contactType": "customer service"
    }
  }
  </script>

  <?php wp_head(); ?>
</head>
<body class="no-scroll">
<header>
  <div class="contentWrapper">
    <div class="col">
      <a href="/" title="Logo | <?php echo get_theme_mod('customTheme-main-callout-title'); ?>" class="logo">
        <img class="lazy" data-src="<?php echo esc_url(wp_get_attachment_url(get_theme_mod('customTheme-main-callout-logo'))); ?>" alt="<?php echo get_theme_mod('customTheme-main-callout-title'); ?>">
      </a>
      <div class="anchorMenu"></div>
    </div>
    <div class="col">
      <div class="socials">
        <a class="social" href="<?php echo esc_html(get_theme_mod('customTheme-main-callout-youtube')); ?>" title="youtube" target="_blank">
          <i class="icon-youtube"></i>
        </a>
        <a class="social" href="<?php echo esc_html(get_theme_mod('customTheme-main-callout-spotify')); ?>" title="spotify" target="_blank">
          <i class="icon-spotify"></i>
        </a>
        <a class="social" href="<?php echo esc_html(get_theme_mod('customTheme-main-callout-facebook')); ?>" title="facebook" target="_blank">
          <i class="icon-facebook"></i>
        </a>
        <a class="social" href="<?php echo esc_html(get_theme_mod('customTheme-main-callout-instagram')); ?>" title="instagram" target="_blank">
          <i class="icon-instagram"></i>
        </a>
      </div>
      <a class="button" href="#Contact" title="Contact">
        Contact
      </a>
    </div>
  </div>
</header>
<div id="pageContainer" class="transition-fade blocks">
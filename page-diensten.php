<?php get_header(); ?>

<section class="heroSection" data-init>
    <div class="heroBackground">
        <!-- Hero background image will be added via CSS -->
    </div>
    <div class="contentWrapper">
        <div class="heroContent">
            <?php if( get_field('hero_title') ): ?>
                <h1 class="heroTitle"><?php the_field('hero_title'); ?></h1>
            <?php endif; ?>
            
            <?php if( get_field('hero_text') ): ?>
                <div class="heroText">
                    <?php 
                    $hero_text = get_field('hero_text');
                    echo wpautop($hero_text);
                    ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
</section>

<div class="mainContent">
    <div class="contentWrapper">
        <div class="cols">
            <div class="col mainCol">
                <?php if( get_field('intro_titel') || get_field('intro_text') ): ?>
                    <div class="introSection">
                        <?php if( get_field('intro_titel') ): ?>
                            <h2 class="normalTitle"><?php the_field('intro_titel'); ?></h2>
                        <?php endif; ?>
                        
                        <?php if( get_field('intro_text') ): ?>
                            <div class="introText">
                                <?php the_field('intro_text'); ?>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>

                <?php if( get_field('werkzaamheden_titel') || have_rows('werkzaamheden') ): ?>
                    <div class="werkzaamhedenSection blue">
                        <div class="werkzaamhedenContent">
                            <?php if( get_field('werkzaamheden_titel') ): ?>
                                <h3 class="werkzaamhedenTitle"><?php the_field('werkzaamheden_titel'); ?></h3>
                            <?php endif; ?>
                            
                            <?php if( have_rows('werkzaamheden') ): ?>
                                <ul class="werkzaamhedenList">
                                    <?php while( have_rows('werkzaamheden') ): the_row(); ?>
                                        <li class="werkzaamhedenItem">
                                            <?php the_sub_field('werkzaamheden_item'); ?>
                                        </li>
                                    <?php endwhile; ?>
                                </ul>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endif; ?>

                <?php if( get_field('onderblok_text') ): ?>
                    <div class="onderblokSection">
                        <div class="onderblokText">
                            <?php 
                            $onderblok_text = get_field('onderblok_text');
                            echo wpautop($onderblok_text);
                            ?>
                        </div>
                    </div>
                <?php endif; ?>
            </div>

            <div class="col sidebarCol">
                <?php if( have_rows('contact_gegevens') ): ?>
                    <?php while( have_rows('contact_gegevens') ): the_row(); ?>
                        <div class="contactCard">
                            <h4 class="contactTitle">RISE & partners</h4>
                            
                            <?php if( get_sub_field('naam') ): ?>
                                <div class="contactName"><?php the_sub_field('naam'); ?></div>
                            <?php endif; ?>
                            
                            <?php if( get_sub_field('adres') ): ?>
                                <div class="contactAddress">
                                    <?php 
                                    $adres = get_sub_field('adres');
                                    echo nl2br(esc_html($adres));
                                    ?>
                                </div>
                            <?php endif; ?>
                            
                            <?php if( get_sub_field('telefoonnummer') ): ?>
                                <div class="contactPhone">
                                    <a href="tel:<?php echo esc_attr(str_replace(' ', '', get_sub_field('telefoonnummer'))); ?>">
                                        <?php the_sub_field('telefoonnummer'); ?>
                                    </a>
                                </div>
                            <?php endif; ?>
                            
                            <?php if( get_sub_field('email') ): ?>
                                <div class="contactEmail">
                                    <a href="mailto:<?php the_sub_field('email'); ?>">
                                        <?php the_sub_field('email'); ?>
                                    </a>
                                </div>
                            <?php endif; ?>
                        </div>
                    <?php endwhile; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php get_footer(); ?>

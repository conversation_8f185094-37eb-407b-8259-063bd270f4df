<?php get_header(); ?>

<section class="heroSection" data-init>
    <div class="contentWrapper">
        <h1 class="hugeTitle">Over RISE & Partners</h1>
    </div>
</section>

<div class="mainContent">
    <div class="contentWrapper">
        <div class="cols">
            <div class="col mainCol">
                <?php if( get_field('intro_text') ): ?>
                    <div class="introText">
                        <?php 
                        $intro_text = get_field('intro_text');
                        echo wpautop($intro_text);
                        ?>
                    </div>
                <?php endif; ?>

                <?php if( get_field('persoon_intro_title') || get_field('persoon_intro_text') ): ?>
                    <div class="persoonSection">
                        <?php if( get_field('persoon_intro_title') ): ?>
                            <h2 class="normalTitle"><?php the_field('persoon_intro_title'); ?></h2>
                        <?php endif; ?>
                        
                        <?php if( get_field('persoon_intro_text') ): ?>
                            <div class="persoonText">
                                <?php the_field('persoon_intro_text'); ?>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>

                <?php if( have_rows('opleidingen') ): ?>
                    <div class="opleidingenSection">
                        <?php if( get_field('opleidingen_titel') ): ?>
                            <h3 class="opleidingenTitle"><?php the_field('opleidingen_titel'); ?></h3>
                        <?php endif; ?>
                        
                        <div class="opleidingenList">
                            <?php while( have_rows('opleidingen') ): the_row(); ?>
                                <div class="opleidingItem">
                                    <?php the_sub_field('opleiding_naam'); ?>
                                </div>
                            <?php endwhile; ?>
                        </div>
                    </div>
                <?php endif; ?>

                <?php if( get_field('quote_tekst') ): ?>
                    <div class="quoteSection">
                        <blockquote class="quoteText">
                            "<?php the_field('quote_tekst'); ?>"
                        </blockquote>
                    </div>
                <?php endif; ?>
            </div>

            <div class="col sidebarCol">
                <?php if( have_rows('contact_gegevens') ): ?>
                    <?php while( have_rows('contact_gegevens') ): the_row(); ?>
                        <div class="contactCard">
                            <h4 class="contactTitle">RISE & partners</h4>
                            
                            <?php if( get_sub_field('naam') ): ?>
                                <div class="contactName"><?php the_sub_field('naam'); ?></div>
                            <?php endif; ?>
                            
                            <?php if( get_sub_field('adres') ): ?>
                                <div class="contactAddress">
                                    <?php 
                                    $adres = get_sub_field('adres');
                                    echo nl2br(esc_html($adres));
                                    ?>
                                </div>
                            <?php endif; ?>
                            
                            <?php if( get_sub_field('telefoonnummer') ): ?>
                                <div class="contactPhone">
                                    <a href="tel:<?php echo esc_attr(str_replace(' ', '', get_sub_field('telefoonnummer'))); ?>">
                                        <?php the_sub_field('telefoonnummer'); ?>
                                    </a>
                                </div>
                            <?php endif; ?>
                            
                            <?php if( get_sub_field('email') ): ?>
                                <div class="contactEmail">
                                    <a href="mailto:<?php the_sub_field('email'); ?>">
                                        <?php the_sub_field('email'); ?>
                                    </a>
                                </div>
                            <?php endif; ?>
                        </div>
                    <?php endwhile; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php get_footer(); ?>
